<div class="user-management-container container-fluid">
  <!-- Header with Add User Button -->
  <div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
      <h3 class="mb-0">User Management</h3>
      <button
        pButton
        type="button"
        class="btn btn-primary add-user-btn"
        (click)="addUser()"
      >
        <i class="pi pi-user-plus mr-2"></i> Add User
      </button>
    </div>
  </div>

  <!-- Filters Section -->
  <div class="card mb-4">
    <div class="card-body table-header">
      <div class="row align-items-end">
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <div class="filter-field search-field">
            <p-inputText
              [(ngModel)]="searchQuery"
              placeholder="Search"
              class="form-control"
              (ngModelChange)="onSearchChange()"
            >
              <ng-template pTemplate="prefix">
                <i class="pi pi-search"></i>
              </ng-template>
            </p-inputText>
          </div>
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <div class="filter-field role-field">
            <p-dropdown
              [(ngModel)]="selectedRole"
              [options]="roleOptions"
              placeholder="Role"
              optionLabel="label"
              optionValue="value"
              (onChange)="onFilterChange()"
              class="w-100"
            >
            </p-dropdown>
          </div>
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <div class="filter-field status-field">
            <p-dropdown
              [(ngModel)]="selectedStatus"
              [options]="statusOptions"
              placeholder="Status"
              optionLabel="label"
              optionValue="value"
              (onChange)="onFilterChange()"
              class="w-100"
            >
            </p-dropdown>
          </div>
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <button
            pButton
            type="button"
            class="btn btn-outline-secondary reset-btn w-100"
            (click)="resetFilters()"
          >
            <i class="pi pi-refresh mr-2"></i> Reset
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Table Section -->
  <div class="card table-card">
    <div class="card-body table-section">
      <div *ngIf="isLoading" class="loading-container">
        <p-progressSpinner></p-progressSpinner>
        <p class="loading-text">Loading users...</p>
      </div>

      <div *ngIf="!isLoading && users.length > 0" class="table-container">
        <p-table
          [value]="users"
          [tableStyle]="{ 'min-width': '50rem' }"
          [paginator]="true"
          [rows]="10"
          [rowsPerPageOptions]="[5, 10, 20]"
          [totalRecords]="totalRecords"
          (onPage)="onPageChange($event)"
        >
          <ng-template pTemplate="header">
            <tr>
              <th>Full Name</th>
              <th>Email</th>
              <th>Roles</th>
              <th>Status</th>
              <th>Created On</th>
              <th>Actions</th>
            </tr>
          </ng-template>
          <ng-template pTemplate="body" let-user>
            <tr>
              <td>
                <div class="user-info">
                  <div
                    class="user-avatar"
                    [ngClass]="{ 'has-image': user.avatar }"
                  >
                    <img
                      *ngIf="user.avatar"
                      [src]="user.avatar"
                      class="avatar-image"
                      alt="User Avatar"
                    />
                    <span *ngIf="!user.avatar" class="avatar-initials">{{
                      getUserInitials(user)
                    }}</span>
                  </div>
                  <div class="user-details">
                    <span class="user-name">{{ user.fullName || "N/A" }}</span>
                    <span class="user-email">{{ user.email || "N/A" }}</span>
                  </div>
                </div>
              </td>
              <td>{{ user.email || "N/A" }}</td>
              <td>
                <div class="roles-container">
                  <span
                    *ngFor="let role of user.roles"
                    class="badge"
                    [ngClass]="{
                      'badge-primary': role === 'Admin',
                      'badge-danger': role === 'Super Admin',
                      'badge-secondary': role === 'User',
                    }"
                  >
                    {{ role }}
                  </span>
                </div>
              </td>
              <td>
                <p-dropdown
                  [(ngModel)]="user.status"
                  [options]="statusOptions"
                  optionLabel="label"
                  optionValue="value"
                  class="status-dropdown"
                  [ngClass]="{
                    'status-active': user.status === 'Active',
                    'status-inactive': user.status === 'Inactive',
                  }"
                  (onChange)="onStatusChange(user, $event.value)"
                >
                  <ng-template pTemplate="selectedItem">
                    <div class="d-flex align-items-center">
                      <i
                        class="pi"
                        [ngClass]="{
                          'pi-check-circle': user.status === 'Active',
                          'pi-times-circle': user.status === 'Inactive',
                        }"
                      ></i>
                      <span>{{ user.status }}</span>
                    </div>
                  </ng-template>
                  <ng-template pTemplate="item" let-option>
                    <div class="d-flex align-items-center">
                      <i
                        class="pi"
                        [ngClass]="{
                          'pi-check-circle': option.value === 'Active',
                          'pi-times-circle': option.value === 'Inactive',
                        }"
                      ></i>
                      <span>{{ option.label }}</span>
                    </div>
                  </ng-template>
                </p-dropdown>
              </td>
              <td class="created-date">
                {{ user.formattedCreatedOn | date: "dd MMM yyyy" }}
              </td>
              <td>
                <div class="actions-container">
                  <button
                    pButton
                    type="button"
                    class="btn btn-outline-success action-btn view-btn"
                    (click)="viewUser(user)"
                  >
                    <i class="pi pi-eye"></i>
                  </button>
                  <button
                    pButton
                    type="button"
                    class="btn btn-outline-warning action-btn edit-btn"
                    (click)="editUser(user)"
                  >
                    <i class="pi pi-pencil"></i>
                  </button>
                </div>
              </td>
            </tr>
          </ng-template>
        </p-table>
      </div>

      <div *ngIf="!isLoading && users.length === 0" class="empty-state">
        <i class="pi pi-users empty-icon"></i>
        <h3>No users found</h3>
        <p>No users match your current filters.</p>
      </div>
    </div>
  </div>
</div>
