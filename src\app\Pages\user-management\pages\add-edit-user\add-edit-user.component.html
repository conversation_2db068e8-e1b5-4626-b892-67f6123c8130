<div class="add-edit-user-container container-fluid">
  <!-- Header with Action Buttons -->
  <div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
      <h3 class="mb-0">{{ isEditMode ? "Edit User" : "Add User" }}</h3>
      <div class="header-actions">
        <button
          pButton
          type="button"
          class="btn btn-outline-secondary mr-2"
          (click)="cancel()"
        >
          <i class="pi pi-times mr-2"></i> Cancel
        </button>
        <button
          pButton
          type="button"
          class="btn btn-primary"
          [disabled]="userForm.invalid"
          (click)="submit()"
        >
          <i class="pi" [ngClass]="isEditMode ? 'pi-save' : 'pi-user-plus'"></i>
          {{ submitButtonText }}
        </button>
      </div>
    </div>
  </div>

  <!-- Form Section -->
  <div class="card form-card">
    <div class="card-body form-section">
      <form [formGroup]="userForm" class="form-section-group">
        <!-- Basic Information -->
        <div class="section-title">Basic Information</div>
        <div class="row form-row">
          <div class="col-12 col-md-6 form-field">
            <label for="fullName" class="form-label">Full Name</label>
            <p-inputText
              id="fullName"
              formControlName="fullName"
              placeholder="Full Name"
              class="form-control"
              [ngClass]="{
                'is-invalid':
                  userForm.get('fullName')?.invalid &&
                  (userForm.get('fullName')?.touched ||
                    userForm.get('fullName')?.dirty),
              }"
            >
              <ng-template pTemplate="prefix">
                <i class="pi pi-user"></i>
              </ng-template>
            </p-inputText>
            <div
              *ngIf="
                userForm.get('fullName')?.invalid &&
                (userForm.get('fullName')?.touched ||
                  userForm.get('fullName')?.dirty)
              "
              class="invalid-feedback"
            >
              <div *ngIf="userForm.get('fullName')?.errors?.['required']">
                Full name is required
              </div>
              <div *ngIf="userForm.get('fullName')?.errors?.['minlength']">
                Full name must be at least 2 characters
              </div>
            </div>
          </div>
          <div class="col-12 col-md-6 form-field">
            <label for="email" class="form-label">Email Address</label>
            <p-inputText
              id="email"
              formControlName="email"
              placeholder="Email Address"
              class="form-control"
              [ngClass]="{
                'is-invalid':
                  userForm.get('email')?.invalid &&
                  (userForm.get('email')?.touched ||
                    userForm.get('email')?.dirty),
              }"
            >
              <ng-template pTemplate="prefix">
                <i class="pi pi-envelope"></i>
              </ng-template>
            </p-inputText>
            <div
              *ngIf="
                userForm.get('email')?.invalid &&
                (userForm.get('email')?.touched || userForm.get('email')?.dirty)
              "
              class="invalid-feedback"
            >
              <div *ngIf="userForm.get('email')?.errors?.['required']">
                Email is required
              </div>
              <div *ngIf="userForm.get('email')?.errors?.['email']">
                Please enter a valid email address
              </div>
            </div>
          </div>
        </div>
        <div class="row form-row">
          <div class="col-12 col-md-6 form-field">
            <label for="phoneNumber" class="form-label">Phone Number</label>
            <p-inputText
              id="phoneNumber"
              formControlName="phoneNumber"
              placeholder="Phone Number"
              class="form-control"
              [ngClass]="{
                'is-invalid':
                  userForm.get('phoneNumber')?.invalid &&
                  (userForm.get('phoneNumber')?.touched ||
                    userForm.get('phoneNumber')?.dirty),
              }"
            >
              <ng-template pTemplate="prefix">
                <i class="pi pi-phone"></i>
              </ng-template>
            </p-inputText>
            <div
              *ngIf="
                userForm.get('phoneNumber')?.invalid &&
                (userForm.get('phoneNumber')?.touched ||
                  userForm.get('phoneNumber')?.dirty)
              "
              class="invalid-feedback"
            >
              <div *ngIf="userForm.get('phoneNumber')?.errors?.['required']">
                Phone number is required
              </div>
              <div *ngIf="userForm.get('phoneNumber')?.errors?.['pattern']">
                Phone number must be a valid number
              </div>
              <div *ngIf="userForm.get('phoneNumber')?.errors?.['minlength']">
                Phone number must be at least 10 digits long
              </div>
            </div>
          </div>
        </div>

        <!-- Roles & Permissions -->
        <div class="section-title">Roles & Permissions</div>
        <div class="row form-row">
          <div class="col-12 col-md-6 form-field">
            <label for="role" class="form-label">User Role</label>
            <p-dropdown
              id="role"
              formControlName="role"
              [options]="roleOptions"
              placeholder="Select Role"
              optionLabel="label"
              optionValue="value"
              class="w-100"
              [ngClass]="{
                'is-invalid':
                  userForm.get('role')?.invalid &&
                  (userForm.get('role')?.touched ||
                    userForm.get('role')?.dirty),
              }"
            >
              <ng-template pTemplate="selectedItem" let-selectedOption>
                <div class="d-flex align-items-center">
                  <i
                    class="pi {{ getRoleIcon(selectedOption.value) }} mr-2"
                  ></i>
                  <span>{{ selectedOption.label }}</span>
                </div>
              </ng-template>
              <ng-template pTemplate="item" let-option>
                <div class="d-flex align-items-center">
                  <i class="pi {{ getRoleIcon(option.value) }} mr-2"></i>
                  <span>{{ option.label }}</span>
                </div>
              </ng-template>
            </p-dropdown>
            <div
              *ngIf="
                userForm.get('role')?.invalid &&
                (userForm.get('role')?.touched || userForm.get('role')?.dirty)
              "
              class="invalid-feedback"
            >
              <div *ngIf="userForm.get('role')?.errors?.['required']">
                Role is required
              </div>
            </div>
          </div>
        </div>

        <!-- Additional Information -->
        <div class="section-title">Additional Information</div>
        <div class="row form-row">
          <div class="col-12 form-field full-width">
            <label for="description" class="form-label">Description</label>
            <p-inputTextarea
              id="description"
              formControlName="description"
              placeholder="Optional description or notes about the user"
              class="form-control"
              [rows]="4"
            >
              <ng-template pTemplate="prefix">
                <i class="pi pi-file"></i>
              </ng-template>
            </p-inputTextarea>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>
