<div class="user-details-container container-fluid">
  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-container">
    <p-progressSpinner></p-progressSpinner>
    <p class="loading-text">Loading user details...</p>
  </div>

  <!-- User Details -->
  <div *ngIf="!isLoading && user" class="profile-section">
    <!-- Header -->
    <div class="card mb-4">
      <div class="card-header header-section">
        <div class="header-content">
          <div class="header-text">
            <h3 class="page-title">{{ user.fullName || "N/A" }}</h3>
            <p class="page-subtitle">{{ user.email || "N/A" }}</p>
          </div>
          <div class="header-navigation">
            <button
              pButton
              type="button"
              class="btn btn-outline-secondary"
              (click)="backToList()"
            >
              <i class="pi pi-arrow-left mr-2"></i> Back to List
            </button>
            <button
              pButton
              type="button"
              class="btn btn-outline-warning"
              (click)="editUser()"
            >
              <i class="pi pi-pencil mr-2"></i> Edit User
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Profile Picture & Basic Information -->
    <div class="card profile-card mb-4">
      <div class="card-body profile-header">
        <div class="profile-picture-container">
          <div class="profile-picture">
            <img
              *ngIf="user.avatar"
              [src]="user.avatar"
              class="profile-image"
              alt="User Avatar"
            />
            <div *ngIf="!user.avatar" class="profile-placeholder">
              <i class="pi pi-user placeholder-icon"></i>
              <span class="initials">{{ getUserInitials(user) }}</span>
            </div>
          </div>
        </div>
        <div class="basic-info-content">
          <div class="basic-info">
            <div class="display-field">
              <i class="pi pi-user field-icon"></i>
              <div class="field-content">
                <span class="field-label">Full Name</span>
                <span class="field-value">{{ user.fullName || "N/A" }}</span>
              </div>
            </div>
            <div class="display-field">
              <i class="pi pi-envelope field-icon"></i>
              <div class="field-content">
                <span class="field-label">Email Address</span>
                <span class="field-value">{{ user.email || "N/A" }}</span>
              </div>
            </div>
            <div class="display-field">
              <i class="pi pi-phone field-icon"></i>
              <div class="field-content">
                <span class="field-label">Phone Number</span>
                <span class="field-value">{{ user.phoneNumber || "N/A" }}</span>
              </div>
            </div>
            <div class="display-field">
              <i
                class="pi"
                [ngClass]="
                  user.isActive ? 'pi-check-circle' : 'pi-times-circle'
                "
                class="field-icon"
              ></i>
              <div class="field-content">
                <span class="field-label">Status</span>
                <span class="field-value">
                  <span
                    class="badge"
                    [ngClass]="{
                      'badge-success': user.isActive,
                      'badge-danger': !user.isActive,
                    }"
                  >
                    {{ user.isActive ? "Active" : "Inactive" }}
                  </span>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Roles & Permissions and Additional Information -->
    <div class="details-grid">
      <!-- Roles & Permissions -->
      <div class="card info-card">
        <div class="card-body form-section-group">
          <div class="section-title">
            <i class="pi pi-shield"></i> Roles & Permissions
          </div>
          <div class="display-field full-width">
            <i class="pi pi-shield field-icon"></i>
            <div class="field-content">
              <span class="field-label">User Role</span>
              <div class="roles-container">
                <span
                  *ngFor="let role of user.roles"
                  class="badge"
                  [ngClass]="{
                    'badge-primary': role === 'User',
                    'badge-warning': role === 'Admin',
                    'badge-danger': role === 'Super Admin',
                  }"
                >
                  <i class="pi {{ getRoleIcon(role) }} mr-1"></i>
                  {{ getRoleDisplayName(role) }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Additional Information -->
      <div class="card info-card">
        <div class="card-body form-section-group">
          <div class="section-title">
            <i class="pi pi-file"></i> Additional Information
          </div>
          <div class="display-field full-width">
            <i class="pi pi-file field-icon"></i>
            <div class="field-content">
              <span class="field-label">Description</span>
              <p class="description-text">
                {{ user.description || "No description provided" }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Error State -->
  <div *ngIf="!isLoading && !user" class="error-state card">
    <div class="card-body text-center">
      <i class="pi pi-exclamation-circle error-icon"></i>
      <h3>User Not Found</h3>
      <p>The requested user could not be found.</p>
      <button
        pButton
        type="button"
        class="btn btn-outline-secondary mt-3"
        (click)="backToList()"
      >
        Back to User List
      </button>
    </div>
  </div>
</div>
