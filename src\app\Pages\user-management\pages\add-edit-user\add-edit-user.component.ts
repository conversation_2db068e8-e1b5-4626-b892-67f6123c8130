import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { UserManagementService } from '../../services/UserManagement.service';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-add-edit-user',
  standalone: false,
  templateUrl: './add-edit-user.component.html',
  styleUrl: './add-edit-user.component.scss',
})
export class AddEditUserComponent implements OnInit {
  userForm: FormGroup;
  isEditMode: boolean = false;
  submitButtonText: string = 'Add User';
  roleOptions = [
    { label: 'Admin', value: 'Admin' },
    { label: 'User', value: 'User' },
    { label: 'Super Admin', value: 'Super Admin' },
  ];
  userId: string | null = null;

  constructor(
    private fb: FormBuilder,
    private userService: UserManagementService,
    private router: Router,
    private route: ActivatedRoute,
  ) {
    this.userForm = this.fb.group({
      fullName: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      phoneNumber: ['', [Validators.required, Validators.pattern(/^\d{10,}$/)]],
      role: ['', Validators.required],
      description: [''],
    });
  }

  ngOnInit(): void {
    this.userId = this.route.snapshot.paramMap.get('id');
    this.isEditMode = !!this.userId;
    this.submitButtonText = this.isEditMode ? 'Save Changes' : 'Add User';

    if (this.isEditMode) {
      this.userService
        .getUserById(this.userId!)
        
        .subscribe({
          next: (user) => {
            if (user) {
              this.userForm.patchValue({
                fullName: user.fullName,
                email: user.email,
                phoneNumber: user.phoneNumber,
                role: user.roles[0] || 'User',
                description: user.description || '',
              });
            }
          },
        });
    }
  }

  getRoleIcon(role: string): string {
    switch (role) {
      case 'Admin':
        return 'pi-shield';
      case 'Super Admin':
        return 'pi-crown';
      case 'User':
        return 'pi-user';
      default:
        return 'pi-user';
    }
  }

  submit(): void {
    if (this.userForm.invalid) return;

    const userData: Partial<User> = {
      fullName: this.userForm.value.fullName,
      email: this.userForm.value.email,
      phoneNumber: this.userForm.value.phoneNumber,
      roles: [this.userForm.value.role],
      status: this.isEditMode
        ? this.userForm.value.status || 'Active'
        : 'Active',
      description: this.userForm.value.description,
    };

    if (this.isEditMode) {
      this.userService
        .updateUser(this.userId!, userData)
        .pipe(first())
        .subscribe({
          next: () => this.router.navigate(['/users']),
          error: () => alert('Failed to update user'),
        });
    } else {
      this.userService
        .addUser(userData as User)
        .pipe(first())
        .subscribe({
          next: () => this.router.navigate(['/users']),
          error: () => alert('Failed to add user'),
        });
    }
  }

  cancel(): void {
    this.router.navigate(['/users']);
  }
}
