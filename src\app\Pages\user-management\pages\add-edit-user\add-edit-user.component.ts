import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { UserManagementService } from '../../services/UserManagement.service';
import { ActivatedRoute, Router } from '@angular/router';
import { first } from 'rxjs/operators';
import {
  UserDetails,
  AddUserRequest,
  UpdateUserRequest,
} from '../../Models/UserManagement';
import { NotificationService } from '../../../../core/Services/Notification.service';

@Component({
  selector: 'app-add-edit-user',
  standalone: false,
  templateUrl: './add-edit-user.component.html',
  styleUrl: './add-edit-user.component.scss',
})
export class AddEditUserComponent implements OnInit {
  userForm: FormGroup;
  isEditMode: boolean = false;
  submitButtonText: string = 'Add User';
  isLoading: boolean = false;
  roleOptions = [
    { label: 'Admin', value: 'Admin' },
    { label: 'User', value: 'User' },
    { label: 'Super Admin', value: 'Super Admin' },
  ];
  userId: string | null = null;

  constructor(
    private fb: FormBuilder,
    private userService: UserManagementService,
    private router: Router,
    private route: ActivatedRoute,
    private notificationService: NotificationService,
  ) {
    this.userForm = this.fb.group({
      fullName: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      phoneNumber: ['', [Validators.required, Validators.pattern(/^\d{10,}$/)]],
      role: ['', Validators.required],
      description: [''],
    });
  }

  ngOnInit(): void {
    this.userId = this.route.snapshot.paramMap.get('id');
    this.isEditMode = !!this.userId;
    this.submitButtonText = this.isEditMode ? 'Save Changes' : 'Add User';

    if (this.isEditMode) {
      this.userService
        .getUserById(this.userId!)

        .subscribe({
          next: (user) => {
            if (user) {
              this.userForm.patchValue({
                fullName: user.fullName,
                email: user.email,
                phoneNumber: user.phoneNumber,
                role: user.roles[0] || 'User',
                description: user.description || '',
              });
            }
          },
        });
    }
  }

  getRoleIcon(role: string): string {
    switch (role) {
      case 'Admin':
        return 'pi-shield';
      case 'Super Admin':
        return 'pi-crown';
      case 'User':
        return 'pi-user';
      default:
        return 'pi-user';
    }
  }

  submit(): void {
    if (this.userForm.invalid) {
      this.userForm.markAllAsTouched();
      return;
    }

    this.isLoading = true;

    if (this.isEditMode) {
      const updateData: UpdateUserRequest = {
        id: this.userId!,
        fullName: this.userForm.value.fullName,
        email: this.userForm.value.email,
        phoneNumber: this.userForm.value.phoneNumber,
        roles: [this.userForm.value.role],
        isActive: true,
        description: this.userForm.value.description,
      };

      this.userService
        .updateUser(this.userId!, updateData)
        .pipe(first())
        .subscribe({
          next: () => {
            this.isLoading = false;
            this.notificationService.showSuccess('User updated successfully!');
            this.router.navigate(['/user-management']);
          },
          error: () => {
            this.isLoading = false;
            this.notificationService.showError('Failed to update user');
          },
        });
    } else {
      const addData: AddUserRequest = {
        fullName: this.userForm.value.fullName,
        email: this.userForm.value.email,
        phoneNumber: this.userForm.value.phoneNumber,
        roles: [this.userForm.value.role],
        isActive: true,
        description: this.userForm.value.description,
        password: 'TempPassword123!', // Default password - should be changed by user
      };

      this.userService
        .addUser(addData)
        .pipe(first())
        .subscribe({
          next: () => {
            this.isLoading = false;
            this.notificationService.showSuccess('User added successfully!');
            this.router.navigate(['/user-management']);
          },
          error: () => {
            this.isLoading = false;
            this.notificationService.showError('Failed to add user');
          },
        });
    }
  }

  cancel(): void {
    this.router.navigate(['/user-management']);
  }
}
