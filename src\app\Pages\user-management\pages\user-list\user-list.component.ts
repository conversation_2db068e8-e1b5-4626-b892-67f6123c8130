import { Component, Injector, OnInit, OnDestroy } from '@angular/core';
import { NotificationService } from '../../../../core/Services';
import { UserManagementService } from '../../services/UserManagement.service';
import { Router } from '@angular/router';
import {
  ModalState,
  SortConfig,
  TableColumn,
  UserDetails,
  UserFilterOptions,
  PaginationParameters,
  UserTableRow,
  UserActionPermissions,
} from '../../Models/UserManagement';
import { FormControl } from '@angular/forms';
import { Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged, takeUntil } from 'rxjs/operators';

@Component({
  selector: 'app-user-list',
  standalone: false,
  templateUrl: './user-list.component.html',
  styleUrl: './user-list.component.scss',
})
export class UserListComponent implements OnInit {
  displayedColumns: string[] = [
    'fullName',
    'email',
    'roles',
    'isActive',
    'createdOn',
    'actions',
  ];

  tableColumns: TableColumn[] = [
    { key: 'fullName', label: 'Full Name', sortable: true, width: '20%' },
    { key: 'email', label: 'Email', sortable: true, width: '25%' },
    { key: 'roles', label: 'Roles', sortable: false, width: '15%' },
    { key: 'isActive', label: 'Status', sortable: true, width: '10%' },
    { key: 'createdOn', label: 'Created On', sortable: true, width: '15%' },
    { key: 'actions', label: 'Actions', sortable: false, width: '15%' },
  ];

  totalItems = 0;
  pageSize = 10;
  pageIndex = 0;
  pageSizeOptions = [5, 10, 25, 50];

  users: UserTableRow[] = [];
  isLoading = false;
  dataSource = { data: [] as UserTableRow[] };

  searchControl = new FormControl('');
  statusFilter = new FormControl('all');
  roleFilter = new FormControl('');
  searchQuery = '';
  selectedRole = '';
  selectedStatus = 'all';

  filterOptions: UserFilterOptions = {
    searchTerm: '',
    statusFilter: 'all',
    roleFilter: '',
    dateRange: { start: null, end: null },
  };

  sortConfig: SortConfig = {
    field: 'createdOn',
    direction: 'desc',
  };

  modalState: ModalState = 'closed';
  selectedUser: UserDetails | null = null;

  currentUserRoles: string[] = [];
  hasAdminAccess = false;

  roleOptions = [
    { label: 'All Roles', value: '' },
    { label: 'Admin', value: 'Admin' },
    { label: 'User', value: 'User' },
    { label: 'Super Admin', value: 'Super Admin' },
  ];

  statusOptions = [
    { label: 'All Status', value: 'all' },
    { label: 'Active', value: 'active' },
    { label: 'Inactive', value: 'inactive' },
  ];

  totalRecords = 0;

  constructor(
    private userManagementService: UserManagementService,
    private notificationService: NotificationService,
    private injector: Injector,
    private router: Router,
  ) {}

  ngOnInit(): void {
    this.setupSearchControls();
    this.loadUsers();

    this.userManagementService.users$.subscribe((pagedResponse) => {
      if (pagedResponse) {
        this.totalItems = pagedResponse.totalItems;
        this.totalRecords = pagedResponse.totalItems;
        this.users = this.transformUsersToTableRows(pagedResponse.items);
        this.dataSource.data = this.users;
      }
      this.isLoading = false;
    });
  }

  private setupSearchControls(): void {
    this.searchControl.valueChanges
      .pipe(debounceTime(300), distinctUntilChanged())
      .subscribe((searchTerm) => {
        this.filterOptions.searchTerm = searchTerm || '';
        this.resetPaginationAndLoad();
      });

    this.statusFilter.valueChanges.subscribe((status) => {
      this.filterOptions.statusFilter = status as 'all' | 'active' | 'inactive';
      this.resetPaginationAndLoad();
    });

    this.roleFilter.valueChanges.subscribe((role) => {
      this.filterOptions.roleFilter = role || '';
      this.resetPaginationAndLoad();
    });
  }

  private loadUsers(): void {
    const params: PaginationParameters = {
      pageNumber: this.pageIndex + 1, 
      pageSize: this.pageSize,
      sortField: this.sortConfig.field,
      sortOrder: this.sortConfig.direction,
      name: this.filterOptions.searchTerm || undefined,
      status:
        this.filterOptions.statusFilter === 'all'
          ? undefined
          : this.filterOptions.statusFilter,
      role: this.filterOptions.roleFilter || undefined,
    };
    this.userManagementService.getUsers(params).subscribe();
  }

  private transformUsersToTableRows(users: UserDetails[]): UserTableRow[] {
    return users.map((user) => ({
      ...user,
      displayRoles: Array.isArray(user.roles)
        ? user.roles.join(', ')
        : (user.roles as string) || 'User',
      statusDisplay: user.isActive ? 'Active' : 'Inactive',
      formattedCreatedOn: this.formatDate(user.createdOn),
      actions: this.getUserActionPermissions(user),
    }));
  }

  private getUserActionPermissions(user: UserDetails): UserActionPermissions {
    const isSuperAdmin = this.currentUserRoles.some(
      (role) =>
        role.toLowerCase() === 'super admin' ||
        role.toLowerCase() === 'superadmin',
    );
    const isAdmin = this.currentUserRoles.some(
      (role) => role.toLowerCase() === 'admin',
    );

    if (isSuperAdmin) {
      return {
        canView: true,
        canEdit: true,
        canToggleStatus: true,
      };
    }

    if (isAdmin) {
      const userIsSuperAdmin = user.roles?.some(
        (role) =>
          role.toLowerCase() === 'super admin' ||
          role.toLowerCase() === 'superadmin',
      );

      return {
        canView: true,
        canEdit: !userIsSuperAdmin, 
        canToggleStatus: !userIsSuperAdmin, 
      };
    }

    return {
      canView: false,
      canEdit: false,
      canToggleStatus: false,
    };
  }

  private formatDate(date: Date): string {
    if (!date) return 'N/A';
    const dateObj = new Date(date);
    return dateObj.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  }

  private resetPaginationAndLoad(): void {
    this.pageIndex = 0;
    this.loadUsers();
  }

  onPageChange(event: any): void {
    this.pageIndex = event.page;
    this.pageSize = event.rows;
    this.loadUsers();
  }

  onSortChange(event: any): void {
    this.sortConfig = {
      field: event.field,
      direction: event.order === 1 ? 'asc' : 'desc',
    };
    this.resetPaginationAndLoad();
  }

  onViewUser(user: UserTableRow): void {
    this.selectedUser = user;
    this.modalState = 'view';
    this.router.navigate(['/user-management/details', user.id]);
  }

  onEditUser(user: UserTableRow): void {
    this.selectedUser = user;
    this.modalState = 'edit';
    this.router.navigate(['/user-management/edit', user.id]);
  }

  onStatusChange(user: UserTableRow, newStatus: boolean): void {
    this.performStatusToggle(user, newStatus);
  }

  private performStatusToggle(user: UserDetails, newStatus: boolean): void {
    if (!user.id) {
      this.notificationService.showError('User ID is missing');
      return;
    }

    const actionText = newStatus ? 'activated' : 'deactivated';

    this.userManagementService
      .deleteUser(user.id, {
        isActive: newStatus,
      })
      .subscribe({
        next: () => {
          this.notificationService.showSuccess(
            `User ${user.fullName || user.email} has been ${actionText} successfully`,
          );
          this.refreshUsers();
        },
      });
  }

  onAddUser(): void {
    this.selectedUser = null;
    this.modalState = 'add';
    // Navigate to add user page
    this.router.navigate(['/user-management/add']);
  }

  refreshUsers(): void {
    this.loadUsers();
  }

  // Methods for template compatibility
  addUser(): void {
    this.onAddUser();
  }

  viewUser(user: UserTableRow): void {
    this.onViewUser(user);
  }

  editUser(user: UserTableRow): void {
    this.onEditUser(user);
  }

  onSearchChange(): void {
    this.filterOptions.searchTerm = this.searchQuery;
    this.resetPaginationAndLoad();
  }

  onFilterChange(): void {
    this.filterOptions.statusFilter = this.selectedStatus as
      | 'all'
      | 'active'
      | 'inactive';
    this.filterOptions.roleFilter = this.selectedRole;
    this.resetPaginationAndLoad();
  }

  resetFilters(): void {
    this.searchQuery = '';
    this.selectedRole = '';
    this.selectedStatus = 'all';
    this.filterOptions = {
      searchTerm: '',
      statusFilter: 'all',
      roleFilter: '',
      dateRange: { start: null, end: null },
    };
    this.resetPaginationAndLoad();
  }

  // The following debug methods were removed as part of service simplification:
  // - debugToken()
  // - debugApiCall()
  // If you need to debug tokens, use browser devtools or add temporary code here.

  /**
   * Get profile picture URL for a user
   */
  getProfilePictureUrl(user: UserTableRow): string | undefined {
    if (!user.profileImageUrl) {
      return undefined;
    }

    // If it's already a full URL, return as is
    if (user.profileImageUrl.startsWith('http')) {
      return user.profileImageUrl;
    }

    // Return the profile image URL as is for now
    return user.profileImageUrl;
  }

  /**
   * Get user initials for avatar fallback
   */
  getUserInitials(user: UserTableRow): string {
    if (user.fullName) {
      const names = user.fullName.split(' ').filter((name) => name.length > 0);
      if (names.length >= 2) {
        return (names[0][0] + names[1][0]).toUpperCase();
      } else if (names.length === 1) {
        return names[0].substring(0, 2).toUpperCase();
      }
    }
    return user.email?.charAt(0).toUpperCase() || 'U';
  }
}
