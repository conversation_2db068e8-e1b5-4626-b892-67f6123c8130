@import "../../../../shared/scss/user-management-variables.scss";

// User Management Container
.user-management-container {
  padding: $spacing-lg;
  background: $user-management-bg;
  min-height: 100vh;
}

// Card Styling
.card {
  background: $card-bg;
  border: 1px solid $card-border;
  border-radius: $radius-lg;
  box-shadow: $shadow-sm;
}

.card-header {
  background: $card-bg;
  border-bottom: 1px solid $card-border;
  padding: $spacing-md $spacing-lg;
}

.card-body.table-header {
  padding: $spacing-md $spacing-lg;
  border-bottom: 1px solid $border-primary;
}

// Filter Fields
.filter-field {
  &.search-field {
    min-width: 240px;
  }

  &.role-field,
  &.status-field {
    min-width: 120px;
  }

  .p-inputtext {
    width: 100%;
  }

  .p-dropdown {
    width: 100%;
    border: 1px solid $border-primary;
    border-radius: $radius-sm;

    .p-dropdown-label {
      color: $text-primary;
    }

    .p-dropdown-trigger {
      color: $text-muted;
    }

    &:hover {
      border-color: $border-secondary;
    }

    &.p-focus {
      border-color: $accent-green;
      box-shadow: 0 0 0 0.2rem rgba($accent-green, 0.25);
    }
  }
}

// Reset Button
.reset-btn {
  height: 38px;
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  border-radius: $radius-sm;

  .pi {
    font-size: 1rem;
  }
}

// Table Section
.table-section {
  margin-bottom: $spacing-lg;
}

.table-card {
  overflow: hidden;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: $spacing-xl;
  gap: $spacing-md;
}

.loading-text {
  color: $text-secondary;
  font-size: 1rem;
  margin: 0;
}

// PrimeNG Table Styling
:host ::ng-deep {
  .p-datatable {
    .p-datatable-header {
      background: $bg-secondary;
      color: $text-primary;
      font-weight: 600;
      font-size: 0.875rem;
      border-bottom: 1px solid $border-primary;
    }

    .p-datatable-thead > tr > th {
      background: $bg-secondary;
      color: $text-primary;
      font-weight: 600;
      font-size: 0.875rem;
      border-bottom: 1px solid $border-primary;
    }

    .p-datatable-tbody > tr > td {
      border-bottom: 1px solid $border-secondary;
      color: $text-primary;
      font-size: 0.875rem;
    }

    .p-datatable-tbody > tr:hover {
      background: $bg-hover;
    }
  }
}

// User Info Column
.user-info {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid $border-primary;
  display: flex;
  align-items: center;
  justify-content: center;
  background: $bg-tertiary;
  flex-shrink: 0;

  .avatar-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .avatar-initials {
    font-size: 1rem;
    font-weight: 600;
    color: $text-primary;
  }

  &.has-image {
    background: transparent;
  }
}

.user-details {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-weight: 500;
  color: $text-primary;
}

.user-email {
  color: $text-secondary;
  font-size: 0.875rem;
}

// Roles Column
.roles-container {
  display: flex;
  flex-wrap: wrap;
  gap: $spacing-xs;
}

.badge {
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: $radius-sm;
}

// Status Column
.status-dropdown {
  min-width: 90px;
  max-width: 140px;
  border-radius: 16px;

  &.status-active {
    .p-dropdown-label {
      background: #e8f5e8;
      color: #2e7d32;
      border-radius: 16px;
      padding: 6px 12px;
    }

    &:hover .p-dropdown-label {
      background: #c8e6c9;
      color: #1b5e20;
    }
  }

  &.status-inactive {
    .p-dropdown-label {
      background: #ffebee;
      color: #d32f2f;
      border-radius: 16px;
      padding: 6px 12px;
    }

    &:hover .p-dropdown-label {
      background: #ffcdd2;
      color: #b71c1c;
    }
  }

  .pi {
    font-size: 1rem;
    margin-right: $spacing-xs;
  }
}

:host ::ng-deep {
  .p-dropdown-panel {
    background: $card-bg;
    border: 1px solid $border-primary;
    border-radius: $radius-md;
    box-shadow: $shadow-lg;

    .p-dropdown-item {
      color: $text-primary;
      font-size: 0.875rem;
      padding: $spacing-sm $spacing-md;

      &:hover {
        background: $bg-hover;
      }
    }
  }
}

// Created Date
.created-date {
  color: $text-secondary;
  font-size: 0.875rem;
}

// Actions Column
.actions-container {
  display: flex;
  gap: $spacing-xs;
}

.action-btn {
  width: 40px;
  height: 40px;
  border-radius: $radius-sm;

  .pi {
    font-size: 1rem;
  }
}

// Empty State
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: $spacing-xl;
  text-align: center;
}

.empty-icon {
  font-size: 4rem;
  color: $text-muted;
  margin-bottom: $spacing-md;
}

.empty-state h3 {
  color: $text-primary;
  font-size: 1.25rem;
  font-weight: 500;
  margin: 0 0 $spacing-sm 0;
}

.empty-state p {
  color: $text-secondary;
  font-size: 1rem;
  margin: 0;
}

// Pagination Styling
:host ::ng-deep {
  .p-paginator {
    background: $card-bg;
    border-top: 1px solid $border-primary;
    color: $text-primary;
    font-size: 0.875rem;
    padding: $spacing-md $spacing-lg;

    .p-paginator-current {
      color: $text-secondary;
    }

    .p-dropdown {
      border: 1px solid $border-primary;
      border-radius: $radius-sm;
      background: $bg-secondary;

      .p-dropdown-label {
        color: $text-primary;
      }

      &:hover {
        background: $bg-hover;
        border-color: $border-secondary;
      }
    }

    .p-paginator-prev,
    .p-paginator-next,
    .p-paginator-first,
    .p-paginator-last {
      color: $text-primary;
      border: 1px solid $border-primary;
      border-radius: $radius-sm;
      background: $bg-secondary;
      width: 36px;
      height: 36px;

      &:hover:not(.p-disabled) {
        background: $bg-hover;
        border-color: $border-secondary;
      }

      &.p-disabled {
        color: $text-muted;
        background: $bg-tertiary;
        border-color: $border-primary;
        opacity: 0.6;
      }

      .pi {
        font-size: 1rem;
      }
    }
  }
}

// Responsive Design
@include media-breakpoint-down(md) {
  .user-management-container {
    padding: $spacing-md;
  }

  .card-header {
    flex-direction: column;
    gap: $spacing-md;
    align-items: stretch;
  }

  .add-user-btn {
    width: 100%;
  }

  .table-header {
    flex-direction: column;
    gap: $spacing-md;
  }

  .filter-field {
    width: 100%;
    min-width: unset;
  }

  .reset-btn {
    width: 100%;
    justify-content: center;
  }

  .user-info {
    flex-direction: column;
    align-items: flex-start;
    gap: $spacing-xs;
  }

  .user-avatar {
    width: 32px;
    height: 32px;

    .avatar-initials {
      font-size: 0.875rem;
    }
  }

  .actions-container {
    flex-direction: column;
    gap: 0.25rem;
  }

  .action-btn {
    width: 32px;
    height: 32px;

    .pi {
      font-size: 0.875rem;
    }
  }

  .p-datatable {
    font-size: 0.75rem;
  }

  .p-paginator {
    padding: $spacing-sm $spacing-md;

    .p-paginator-current {
      font-size: 0.75rem;
      text-align: center;
      flex: 1;
    }

    .p-paginator-prev,
    .p-paginator-next,
    .p-paginator-first,
    .p-paginator-last {
      width: 32px;
      height: 32px;

      .pi {
        font-size: 0.875rem;
      }
    }
  }
}

// Light Theme Overrides
:host-context(.light-theme) {
  .user-management-container {
    background: $user-management-bg;
  }

  .card {
    background: $card-bg;
    border-color: $card-border;
  }

  .card-header {
    background: $card-bg;
    border-bottom-color: $card-border;
  }

  .filter-field {
    .p-dropdown {
      .p-dropdown-label {
        color: $text-primary;
      }

      .p-dropdown-trigger {
        color: $text-muted;
      }

      &:hover {
        border-color: $border-secondary;
      }

      &.p-focus {
        border-color: $accent-green;
        box-shadow: 0 0 0 0.2rem rgba($accent-green, 0.25);
      }
    }
  }

  .reset-btn {
    background: $card-bg;
    color: $text-primary;
    border-color: $card-border;

    &:hover {
      background: $bg-hover;
      border-color: $border-secondary;
    }
  }

  .p-datatable {
    .p-datatable-thead > tr > th {
      background: $bg-secondary;
      color: $text-primary;
      border-bottom-color: $card-border;
    }

    .p-datatable-tbody > tr > td {
      border-bottom-color: $border-secondary;
      color: $text-primary;
    }

    .p-datatable-tbody > tr:hover {
      background: $bg-hover;
    }
  }

  .user-name {
    color: $text-primary;
  }

  .user-email {
    color: $text-secondary;
  }

  .created-date {
    color: $text-secondary;
  }

  .loading-text {
    color: $text-secondary;
  }

  .empty-icon {
    color: $text-muted;
  }

  .empty-state h3 {
    color: $text-primary;
  }

  .empty-state p {
    color: $text-secondary;
  }

  .p-paginator {
    background: $card-bg;
    border-top-color: $card-border;
    color: $text-primary;

    .p-paginator-current {
      color: $text-secondary;
    }

    .p-dropdown {
      .p-dropdown-label {
        color: $text-primary;
      }

      &:hover {
        background: $bg-hover;
        border-color: $border-secondary;
      }
    }

    .p-paginator-prev,
    .p-paginator-next,
    .p-paginator-first,
    .p-paginator-last {
      color: $text-primary;
      border-color: $card-border;
      background: $bg-secondary;

      &:hover:not(.p-disabled) {
        background: $bg-hover;
        border-color: $border-secondary;
      }

      &.p-disabled {
        color: $text-muted;
        background: $bg-tertiary;
        border-color: $card-border;
      }
    }
  }
}

// Dark Theme Overrides
:host-context(.dark-theme) {
  .user-management-container {
    background: $dark-user-management-bg;
  }

  .card {
    background: $dark-card-bg;
    border-color: $dark-card-border;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }

  .card-header {
    background: $dark-card-bg;
    border-bottom-color: $dark-card-border;
  }

  .filter-field {
    .p-dropdown {
      .p-dropdown-label {
        color: $dark-text-primary;
      }

      .p-dropdown-trigger {
        color: $dark-text-muted;
      }

      &:hover {
        border-color: $dark-border-secondary;
      }

      &.p-focus {
        border-color: $accent-green;
        box-shadow: 0 0 0 0.2rem rgba($accent-green, 0.25);
      }
    }
  }

  .reset-btn {
    background: $dark-card-bg;
    color: $dark-text-primary;
    border-color: $dark-card-border;

    &:hover {
      background: $dark-bg-hover;
      border-color: $dark-border-secondary;
    }
  }

  .p-datatable {
    .p-datatable-thead > tr > th {
      background: $dark-bg-secondary;
      color: $dark-text-primary;
      border-bottom-color: $dark-card-border;
    }

    .p_datatable-tbody > tr > td {
      border-bottom-color: $dark-border-secondary;
      color: $dark-text-primary;
    }

    .p-datatable-tbody > tr:hover {
      background: $dark-bg-hover;
    }
  }

  .p-paginator {
    background: $dark-card-bg;
    border-top-color: $dark-card-border;
    color: $dark-text-primary;

    .p-paginator-current {
      color: $dark-text-secondary;
    }

    .p-dropdown {
      .p-dropdown-label {
        color: $dark-text-primary;
      }

      &:hover {
        background: $dark-bg-hover;
        border-color: $dark-border-secondary;
      }
    }

    .p-paginator-prev,
    .p-paginator-next,
    .p-paginator-first,
    .p-paginator-last {
      color: $dark-text-primary;
      border-color: $dark-card-border;
      background: $dark-bg-secondary;

      &:hover:not(.p-disabled) {
        background: $dark-bg-hover;
        border-color: $dark-border-secondary;
      }

      &.p-disabled {
        color: $dark-text-muted;
        background: $dark-bg-tertiary;
        border-color: $dark-card-border;
      }
    }
  }

  :host ::ng-deep {
    .p-dropdown-panel {
      background: $dark-card-bg;
      border-color: $dark-card-border;

      .p-dropdown-item {
        color: $dark-text-primary;
        background: $dark-card-bg;

        &:hover {
          background: $dark-bg-hover;
        }
      }
    }
  }
}
