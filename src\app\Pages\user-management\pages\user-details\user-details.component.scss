@import "../../../../shared/scss/user-management-variables.scss";

// User Details Container
.user-details-container {
  padding: $spacing-lg;
  background: $user-management-bg;
  min-height: 100vh;
  width: 100%;
  position: relative;
}

// Header Section
.header-section {
  margin-bottom: $spacing-lg;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: $spacing-md;
}

.header-navigation {
  display: flex;
  align-items: flex-start;
  gap: $spacing-md;
}

.header-text {
  flex: 1;
}

.page-title {
  color: $text-primary;
  font-size: 2rem;
  font-weight: 600;
  margin: 0 0 $spacing-xs 0;
}

.page-subtitle {
  color: $text-secondary;
  font-size: 1rem;
  margin: 0;
  line-height: 1.5;
}

.header-actions {
  display: flex;
  gap: $spacing-sm;
}

// Loading Container
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: $spacing-xl;
  gap: $spacing-md;
}

.loading-text {
  color: $text-secondary;
  font-size: 1rem;
  margin: 0;
}

// Form Section
.form-section {
  width: 100%;
  margin: 0 auto;
}

.form-card {
  background: $card-bg;
  border: 1px solid $card-border;
  border-radius: $radius-lg;
  box-shadow: $shadow-sm;
  overflow: hidden;
}

.form-section-group {
  margin-top: $spacing-lg;
  margin-bottom: $spacing-xl;
  padding: 0 $spacing-lg;
}

.section-title {
  color: $text-primary;
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 $spacing-md 0;
  display: flex;
  align-items: center;
  gap: $spacing-sm;

  .pi {
    font-size: 1.5rem;
  }
}

// Form Rows
.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: $spacing-lg;
  margin-bottom: $spacing-md;

  &:last-child {
    margin-bottom: 0;
  }
}

// Display Fields
.display-field {
  display: flex;
  align-items: flex-start;
  gap: $spacing-md;
  padding: $spacing-md;
  border: 1px solid $border-secondary;
  border-radius: $radius-md;
  background: $bg-secondary;
  transition: all 0.2s ease;

  &.full-width {
    grid-column: 1 / -1;
  }

  &:hover {
    border-color: $border-primary;
    background: $bg-hover;
  }
}

.field-icon {
  color: $text-secondary;
  font-size: 1.5rem;
  width: 1.5rem;
  height: 1.5rem;
  margin-top: 2px;
  flex-shrink: 0;
}

.field-content {
  flex: 1;
  min-width: 0;
}

.field-label {
  display: block;
  color: $text-secondary;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: $spacing-xs;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.field-value {
  color: $text-primary;
  font-size: 1rem;
  line-height: 1.5;
  word-break: break-word;
}

// Profile Picture Container
.profile-picture-container {
  display: flex;
  justify-content: center;
  padding: $spacing-md 0;
}

.basic-info-content {
  padding-left: $spacing-md;
}

// Profile Section
.profile-card {
  background: $card-bg;
  border: 1px solid $card-border;
  border-radius: $radius-lg;
  box-shadow: $shadow-sm;
}

.profile-header {
  display: flex;
  align-items: center;
  gap: $spacing-lg;
  padding: $spacing-xl;
}

.profile-picture {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.profile-image {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid $border-primary;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.profile-placeholder {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: $bg-secondary;
  border: 3px solid $border-primary;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.placeholder-icon {
  font-size: 4rem;
  color: $text-muted;
  opacity: 0.3;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
}

.initials {
  font-size: 2rem;
  font-weight: 700;
  color: $text-primary;
  text-transform: uppercase;
  letter-spacing: 1px;
  z-index: 2;
  position: relative;
}

.basic-info {
  flex: 1;
}

.user-name {
  color: $text-primary;
  font-size: 1.75rem;
  font-weight: 600;
  margin: 0 0 $spacing-xs 0;
}

.user-email {
  color: $text-secondary;
  font-size: 1.125rem;
  margin: 0 0 $spacing-md 0;
}

.badge {
  display: inline-flex;
  align-items: center;
  gap: $spacing-xs;
  padding: $spacing-xs $spacing-sm;
  border-radius: 40px;
  font-weight: 500;

  &.badge-success {
    background: #e8f5e8;
    color: #2e7d32;

    .pi {
      color: #2e7d32;
      font-size: 1.125rem;
    }
  }

  &.badge-danger {
    background: #ffebee;
    color: #c62828;

    .pi {
      color: #c62828;
      font-size: 1.125rem;
    }
  }

  &.badge-primary {
    background: #e3f2fd;
    color: #1565c0;

    .pi {
      color: #1565c0;
    }
  }

  &.badge-warning {
    background: #fff3e0;
    color: #ef6c00;

    .pi {
      color: #ef6c00;
    }
  }
}

// Details Grid
.details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: $spacing-lg;
}

.info-card {
  background: $card-bg;
  border: 1px solid $card-border;
  border-radius: $radius-lg;
  box-shadow: $shadow-sm;
}

.roles-container {
  display: flex;
  flex-wrap: wrap;
  gap: $spacing-xs;
  margin-top: $spacing-xs;
}

.description-text {
  color: $text-primary;
  font-size: 0.875rem;
  line-height: 1.6;
  margin: 0;
  padding: $spacing-sm 0;
}

// Error State
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: $spacing-xl;
  text-align: center;
  gap: $spacing-md;
}

.error-icon {
  font-size: 4rem;
  color: $text-muted;
}

.error-state h3 {
  color: $text-primary;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

.error-state p {
  color: $text-secondary;
  font-size: 1rem;
  margin: 0;
}

// Responsive Design
@include media-breakpoint-down(md) {
  .user-details-container {
    padding: $spacing-md;
  }

  .form-section {
    max-width: 100%;
  }

  .page-title {
    font-size: 1.5rem;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: $spacing-md;
  }

  .display-field {
    padding: $spacing-sm;
  }

  .field-icon {
    font-size: 1.25rem;
    width: 1.25rem;
    height: 1.25rem;
  }

  .profile-image,
  .profile-placeholder {
    width: 100px;
    height: 100px;
  }

  .placeholder-icon {
    font-size: 3rem;
  }

  .initials {
    font-size: 1.5rem;
  }

  .section-title {
    font-size: 1.125rem;
  }

  .field-label {
    font-size: 0.8rem;
  }

  .field-value {
    font-size: 0.9rem;
  }

  .basic-info-content {
    padding-left: 0;
    margin-top: $spacing-md;
  }

  .header-content {
    flex-direction: column;
    align-items: stretch;
  }

  .header-navigation {
    justify-content: space-between;
  }

  .header-navigation .btn {
    width: 48%;
  }
}

// Light Theme Overrides
:host-context(.light-theme) {
  .user-details-container {
    background: $user-management-bg;
  }

  .profile-card,
  .info-card {
    background: $card-bg;
    border-color: $card-border;
    box-shadow: $shadow-sm;
  }

  .page-title {
    color: $text-primary;
  }

  .page-subtitle {
    color: $text-secondary;
  }

  .user-name {
    color: $text-primary;
  }

  .user-email {
    color: $text-secondary;
  }

  .profile-placeholder {
    background: $bg-secondary;
    border-color: $border-primary;
  }

  .placeholder-icon {
    color: $text-muted;
  }

  .initials {
    color: $text-primary;
  }

  .profile-image {
    border-color: $border-primary;
  }

  .info-label {
    color: $text-secondary;
  }

  .info-value {
    color: $text-primary;
  }

  .description-text {
    color: $text-primary;
  }

  .loading-text {
    color: $text-secondary;
  }

  .error-icon {
    color: $text-muted;
  }

  .error-state h3 {
    color: $text-primary;
  }

  .error-state p {
    color: $text-secondary;
  }

  .info-row {
    border-bottom-color: $border-secondary;
  }
}

// Dark Theme Overrides
:host-context(.dark-theme) {
  .user-details-container {
    background: $dark-user-management-bg;
  }

  .profile-card,
  .info-card {
    background: $dark-card-bg;
    border-color: $dark-card-border;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }

  .page-title {
    color: $dark-text-primary;
  }

  .page-subtitle {
    color: $dark-text-secondary;
  }

  .user-name {
    color: $dark-text-primary;
  }

  .user-email {
    color: $dark-text-secondary;
  }

  .profile-placeholder {
    background: $dark-bg-secondary;
    border-color: $dark-border-primary;
  }

  .placeholder-icon {
    color: $dark-text-muted;
  }

  .initials {
    color: $dark-text-primary;
  }

  .profile-image {
    border-color: $dark-border-primary;
  }

  .info-label {
    color: $dark-text-secondary;
  }

  .info-value {
    color: $dark-text-primary;
  }

  .description-text {
    color: $dark-text-primary;
  }

  .loading-text {
    color: $dark-text-secondary;
  }

  .error-icon {
    color: $dark-text-muted;
  }

  .error-state h3 {
    color: $dark-text-primary;
  }

  .error-state p {
    color: $dark-text-secondary;
  }

  .info-row {
    border-bottom-color: $dark-border-secondary;
  }

  .badge {
    &.badge-success {
      background: #1b4332;
      color: #4caf50;

      .pi {
        color: #4caf50;
      }
    }

    &.badge-danger {
      background: #4a1e1e;
      color: #f44336;

      .pi {
        color: #f44336;
      }
    }

    &.badge-primary {
      background: #1e3a8a;
      color: #60a5fa;

      .pi {
        color: #60a5fa;
      }
    }

    &.badge-warning {
      background: #92400e;
      color: #fbbf24;

      .pi {
        color: #fbbf24;
      }
    }
  }
}
