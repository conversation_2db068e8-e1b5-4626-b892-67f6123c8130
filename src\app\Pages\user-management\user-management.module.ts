import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';

import { UserManagementRoutingModule } from './user-management-routing.module';
import { UserListComponent } from './pages/user-list/user-list.component';
import { AddEditUserComponent } from './pages/add-edit-user/add-edit-user.component';
import { UserDetailsComponent } from './pages/user-details/user-details.component';

// PrimeNG Modules
import { TableModule } from 'primeng/table';
import { PaginatorModule } from 'primeng/paginator';
import { InputTextModule } from 'primeng/inputtext';

import { DropdownModule } from 'primeng/dropdown';
import { ButtonModule } from 'primeng/button';
import { InputSwitchModule } from 'primeng/inputswitch';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { ToastModule } from 'primeng/toast';
import { MessageService } from 'primeng/api';

@NgModule({
  declarations: [UserListComponent, AddEditUserComponent, UserDetailsComponent],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    UserManagementRoutingModule,
    // PrimeNG Modules
    TableModule,
    PaginatorModule,
    InputTextModule,

    DropdownModule,
    ButtonModule,
    InputSwitchModule,
    ProgressSpinnerModule,
    ToastModule,
  ],
  providers: [MessageService],
})
export class UserManagementModule {}
