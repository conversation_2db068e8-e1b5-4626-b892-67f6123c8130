import { Component } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { UserManagementService } from '../../services/UserManagement.service';
import { UserDetails } from '../../Models/UserManagement';
import { Subject } from 'rxjs';

@Component({
  selector: 'app-user-details',
  standalone: false,
  templateUrl: './user-details.component.html',
  styleUrl: './user-details.component.scss',
})
export class UserDetailsComponent {
  private destroy$ = new Subject<void>();

  user: UserDetails | null = null;
  userId: string | null = null;
  isLoading = false;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private userManagementService: UserManagementService,
  ) {}

  ngOnInit(): void {
    this.loadUserFromRoute();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private loadUserFromRoute(): void {
    this.userId = this.route.snapshot.paramMap.get('id');
    if (this.userId) {
      this.loadUserDetails(this.userId);
    } else {
      this.navigateToUserList();
    }
  }

  private loadUserDetails(userId: string): void {
    this.isLoading = true;
    this.userManagementService
      .getUserById(userId)

      .subscribe({
        next: (user: UserDetails) => {
          this.user = user;
          this.isLoading = false;
        },
        error: () => {
          this.isLoading = false;
          this.user = null;
        },
      });
  }

  // getProfilePictureUrl(user: UserDetails): string | undefined {
  //   if (!user.profileImageUrl) {
  //     return undefined;
  //   }

  //   // If it's already a full URL, return as is
  //   if (user.profileImageUrl.startsWith('http')) {
  //     return user.profileImageUrl;
  //   }

  // }

  getUserInitials(user: UserDetails): string {
    if (user.fullName) {
      const names = user.fullName.split(' ').filter((name) => name.length > 0);
      if (names.length >= 2) {
        return (names[0][0] + names[1][0]).toUpperCase();
      } else if (names.length === 1) {
        return names[0].substring(0, 2).toUpperCase();
      }
    }
    return user.email?.charAt(0).toUpperCase() || 'U';
  }

  formatDate(date: Date): string {
    if (!date) return 'N/A';
    const dateObj = new Date(date);
    return dateObj.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  }

  // getRoleClass(role: string): string {
  //   const normalizedRole = role.toLowerCase().replace(' ', '-');
  //   return `role-${normalizedRole}`;
  // }

  // getRoleDisplayName(role: string): string {
  //   return role.charAt(0).toUpperCase() + role.slice(1);
  // }

  // getRoleIcon(role: string): string {
  //   const roleIcons = {
  //     User: 'person',
  //     Admin: 'admin_panel_settings',
  //     'Super Admin': 'supervisor_account',
  //   };
  //   return roleIcons[role as keyof typeof roleIcons] || 'person';
  // }

  onEditUser(): void {
    if (this.user?.id) {
      this.router.navigate(['/user-management/edit', this.user.id]);
    }
  }

  onBackToList(): void {
    this.navigateToUserList();
  }

  private navigateToUserList(): void {
    this.router.navigate(['/user-management/list']);
  }
}
