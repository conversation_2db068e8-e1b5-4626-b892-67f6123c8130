@import "../../../../shared/scss/user-management-variables.scss";

// Add/Edit User Container
.add-edit-user-container {
  padding: $spacing-lg;
  background: $user-management-bg;
  min-height: 100vh;
  width: 100%;
  position: relative;
}

// Card Styling
.card {
  background: $card-bg;
  border: 1px solid $card-border;
  border-radius: $radius-lg;
  box-shadow: $shadow-sm;
}

.card-header {
  background: $card-bg;
  border-bottom: 1px solid $card-border;
  padding: $spacing-md $spacing-lg;
}

// Section Titles
.section-title {
  color: $text-primary;
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 $spacing-md 0;
  padding-bottom: $spacing-sm;
  border-bottom: 1px solid $border-secondary;
}

// Form Section
.form-section {
  width: 100%;
}

.form-card {
  overflow: hidden;
}

.form-section-group {
  margin-top: $spacing-lg;
  margin-bottom: $spacing-xl;
  padding: 0 $spacing-lg;

  &:last-child {
    margin-bottom: $spacing-lg;
  }
}

// Form Rows and Fields
.form-row {
  margin-bottom: $spacing-md;

  &:last-child {
    margin-bottom: 0;
  }
}

.form-field {
  .form-label {
    color: $text-secondary;
    font-size: 0.875rem;
    margin-bottom: $spacing-xs;
  }

  .p-inputtext,
  .p-inputtextarea {
    width: 100%;
    border: 1px solid $border-primary;
    border-radius: $radius-sm;
    color: $text-primary;

    &:hover {
      border-color: $border-secondary;
    }

    &.p-focus {
      border-color: $accent-green;
      box-shadow: 0 0 0 0.2rem rgba($accent-green, 0.25);
    }

    &.ng-invalid.ng-touched,
    &.ng-invalid.ng-dirty {
      border-color: $error;
    }
  }

  .p-dropdown {
    width: 100%;
    border: 1px solid $border-primary;
    border-radius: $radius-sm;

    .p-dropdown-label {
      color: $text-primary;
    }

    .p-dropdown-trigger {
      color: $text-muted;
    }

    &:hover {
      border-color: $border-secondary;
    }

    &.p-focus {
      border-color: $accent-green;
      box-shadow: 0 0 0 0.2rem rgba($accent-green, 0.25);
    }

    &.ng-invalid.ng-touched,
    &.ng-invalid.ng-dirty {
      border-color: $error;
    }
  }

  &.full-width {
    width: 100%;
  }

  .pi {
    font-size: 1rem;
    margin-right: $spacing-xs;
  }

  .invalid-feedback {
    color: $error;
    font-size: 0.75rem;
  }
}

:host ::ng-deep {
  .p-dropdown-panel {
    background: $card-bg;
    border: 1px solid $border-primary;
    border-radius: $radius-md;
    box-shadow: $shadow-lg;

    .p-dropdown-item {
      color: $text-primary;
      font-size: 0.875rem;
      padding: $spacing-sm $spacing-md;

      &:hover {
        background: $bg-hover;
      }
    }
  }
}

// Responsive Design
@include media-breakpoint-down(md) {
  .add-edit-user-container {
    padding: $spacing-md;
  }

  .card-header {
    flex-direction: column;
    gap: $spacing-md;
    align-items: stretch;

    .header-actions {
      width: 100%;
      display: flex;
      justify-content: space-between;
    }

    .btn {
      width: 48%;
    }
  }

  .form-card {
    padding: $spacing-md;
  }

  .form-row {
    flex-direction: column;
    gap: $spacing-sm;
  }
}

// Light Theme Overrides
:host-context(.light-theme) {
  .add-edit-user-container {
    background: $user-management-bg;
  }

  .card {
    background: $card-bg;
    border-color: $card-border;
  }

  .card-header {
    background: $card-bg;
    border-bottom-color: $card-border;
  }

  .section-title {
    color: $text-primary;
    border-bottom-color: $border-secondary;
  }

  .form-field {
    .form-label {
      color: $text-secondary;
    }

    .p-inputtext,
    .p-inputtextarea,
    .p-dropdown {
      border-color: $border-primary;

      &:hover {
        border-color: $border-secondary;
      }

      &.p-focus {
        border-color: $accent-green;
        box-shadow: 0 0 0 0.2rem rgba($accent-green, 0.25);
      }

      &.ng-invalid.ng-touched,
      &.ng-invalid.ng-dirty {
        border-color: $error;
      }
    }

    .p-dropdown {
      .p-dropdown-label {
        color: $text-primary;
      }

      .p-dropdown-trigger {
        color: $text-muted;
      }
    }
  }
}

// Dark Theme Overrides
:host-context(.dark-theme) {
  .add-edit-user-container {
    background: $dark-user-management-bg;
  }

  .card {
    background: $dark-card-bg;
    border-color: $dark-card-border;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }

  .card-header {
    background: $dark-card-bg;
    border-bottom-color: $dark-card-border;
  }

  .section-title {
    color: $dark-text-primary;
    border-bottom-color: $dark-border-secondary;
  }

  .form-field {
    .form-label {
      color: $dark-text-secondary;
    }

    .p-inputtext,
    .p-inputtextarea,
    .p-dropdown {
      border-color: $dark-border-primary;
      color: $dark-text-primary;

      &:hover {
        border-color: $dark-border-secondary;
      }

      &.p-focus {
        border-color: $accent-green;
        box-shadow: 0 0 0 0.2rem rgba($accent-green, 0.25);
      }

      &.ng-invalid.ng-touched,
      &.ng-invalid.ng-dirty {
        border-color: $error;
      }
    }

    .p-dropdown {
      .p-dropdown-label {
        color: $dark-text-primary;
      }

      .p-dropdown-trigger {
        color: $dark-text-muted;
      }
    }
  }

  :host ::ng-deep {
    .p-dropdown-panel {
      background: $dark-card-bg;
      border-color: $dark-card-border;

      .p-dropdown-item {
        color: $dark-text-primary;
        background: $dark-card-bg;

        &:hover {
          background: $dark-bg-hover;
        }
      }
    }
  }
}
