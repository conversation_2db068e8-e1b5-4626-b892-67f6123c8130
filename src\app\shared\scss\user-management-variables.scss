@import "~bootstrap/scss/functions";
@import "~bootstrap/scss/variables";
@import "~bootstrap/scss/mixins";

// Common variables for user management components
$user-management-bg: var(--bs-body-bg);
$card-bg: var(--bs-white);
$card-border: var(--bs-gray-300);
$shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
$shadow-lg: 0 4px 12px rgba(0, 0, 0, 0.15);
$accent-green: #28a745;
$text-primary: var(--bs-dark);
$text-secondary: var(--bs-gray-600);
$text-muted: var(--bs-gray-500);
$bg-hover: var(--bs-light);
$border-primary: var(--bs-gray-300);
$border-secondary: var(--bs-gray-400);
$bg-secondary: var(--bs-gray-100);
$bg-tertiary: var(--bs-gray-200);
$error: var(--bs-danger);

$spacing-xs: 0.25rem;
$spacing-sm: 0.5rem;
$spacing-md: 1rem;
$spacing-lg: 1.5rem;
$spacing-xl: 2rem;

$radius-sm: 0.25rem;
$radius-md: 0.5rem;
$radius-lg: 0.75rem;

// Dark theme overrides
$dark-user-management-bg: #212529;
$dark-card-bg: #2a2a2a;
$dark-card-border: #404040;
$dark-text-primary: #ffffff;
$dark-text-secondary: #b0b0b0;
$dark-text-muted: #666666;
$dark-bg-hover: #353535;
$dark-bg-secondary: #404040;
$dark-bg-tertiary: #353535;
